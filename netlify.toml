[build]
  command = "npm run build"

# Use Netlify's Next.js plugin for full Next.js support
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Headers for CORS and security
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

[[headers]]
  for = "/company_knowledge.json"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Cache-Control = "public, max-age=3600"

# Block Facebook tracking and unwanted third-party scripts
[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://assets.calendly.com https://calendly.com https://api.openai.com; connect-src 'self' https://assets.calendly.com https://calendly.com https://api.openai.com https://upzera-web.netlify.app; frame-src 'self' https://calendly.com; img-src 'self' data: https:; style-src 'self' 'unsafe-inline' https:; font-src 'self' https:; object-src 'none'; base-uri 'self';"
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"



# Environment variables (these will be set in Netlify dashboard)
# MAILGUN_API_KEY
# MAILGUN_DOMAIN
# MAILGUN_FROM
# MAILGUN_REGION
# NEXT_PUBLIC_OPENAI_API_KEY
