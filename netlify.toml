[build]
  command = "npm run build"

# Use Netlify's Next.js plugin for full Next.js support
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Headers for CORS and security
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

[[headers]]
  for = "/company_knowledge.json"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Cache-Control = "public, max-age=3600"

# CSP headers for Calendly integration
[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://assets.calendly.com https://calendly.com https://js.intercomcdn.com https://widget.intercom.io; style-src 'self' 'unsafe-inline' https://assets.calendly.com https://calendly.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://assets.calendly.com; img-src 'self' data: blob: https: http:; connect-src 'self' https://api.calendly.com https://calendly.com https://assets.calendly.com wss://nexus-websocket-a.intercom.io https://api-iam.intercom.io; frame-src 'self' https://calendly.com https://assets.calendly.com; media-src 'self' https://assets.calendly.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'"

# Environment variables (these will be set in Netlify dashboard)
# MAILGUN_API_KEY
# MAILGUN_DOMAIN
# MAILGUN_FROM
# MAILGUN_REGION
# NEXT_PUBLIC_OPENAI_API_KEY
