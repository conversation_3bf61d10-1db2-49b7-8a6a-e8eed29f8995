[build]
  command = "npm run build"

# Use Netlify's Next.js plugin for full Next.js support
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Headers for CORS and security
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

[[headers]]
  for = "/company_knowledge.json"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Cache-Control = "public, max-age=3600"



# Environment variables (these will be set in Netlify dashboard)
# MAILGUN_API_KEY
# MAILGUN_DOMAIN
# MAILGUN_FROM
# MAILGUN_REGION
# NEXT_PUBLIC_OPENAI_API_KEY
