"use client";

import { useEffect } from 'react';

/**
 * Component to block unwanted tracking scripts and clean up console errors
 * Specifically targets Facebook tracking and other third-party analytics
 */
export default function ScriptBlocker() {
  useEffect(() => {
    // Block Facebook tracking scripts
    const blockFacebookTracking = () => {
      // Override Facebook tracking functions if they exist
      if (typeof window !== 'undefined') {
        // Block Facebook Pixel
        window.fbq = window.fbq || function() {
          console.log('🚫 Facebook Pixel blocked');
        };
        
        // Block Facebook SDK
        window.FB = window.FB || {
          init: () => console.log('🚫 Facebook SDK blocked'),
          AppEvents: {
            logEvent: () => console.log('🚫 Facebook AppEvents blocked'),
            logPageView: () => console.log('🚫 Facebook PageView blocked')
          }
        };

        // Block other common tracking
        window.gtag = window.gtag || function() {
          console.log('🚫 Google Analytics blocked');
        };
        
        window.ga = window.ga || function() {
          console.log('🚫 Google Analytics (legacy) blocked');
        };
      }
    };

    // Block unwanted network requests
    const blockUnwantedRequests = () => {
      // Override fetch to block Facebook tracking requests
      const originalFetch = window.fetch;
      window.fetch = function(input, init) {
        const url = typeof input === 'string' ? input : input.url;
        
        // Block Facebook tracking domains
        const blockedDomains = [
          'connect.facebook.net',
          'facebook.com/tr',
          'facebook.com/plugins',
          'doubleclick.net',
          'google-analytics.com',
          'googletagmanager.com'
        ];
        
        if (blockedDomains.some(domain => url.includes(domain))) {
          console.log(`🚫 Blocked request to: ${url}`);
          return Promise.reject(new Error('Request blocked by ScriptBlocker'));
        }
        
        return originalFetch.call(this, input, init);
      };
    };

    // Clean up console errors by overriding console.error for specific patterns
    const cleanConsoleErrors = () => {
      const originalError = console.error;
      console.error = function(...args) {
        const message = args.join(' ');
        
        // Skip Facebook-related errors
        if (
          message.includes('connect.facebook.net') ||
          message.includes('ERR_BLOCKED_BY_CLIENT') ||
          message.includes('facebook') ||
          message.includes('fbevents')
        ) {
          return; // Don't log these errors
        }
        
        // Log other errors normally
        originalError.apply(console, args);
      };
    };

    // Initialize all blocking mechanisms
    blockFacebookTracking();
    blockUnwantedRequests();
    cleanConsoleErrors();

    // Clean up on unmount
    return () => {
      // Restore original functions if needed
      // (In practice, this component should stay mounted)
    };
  }, []);

  // This component doesn't render anything
  return null;
}
