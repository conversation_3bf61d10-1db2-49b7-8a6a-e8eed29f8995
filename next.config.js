/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true
  },
  // Ensure compatibility with Netlify
  trailingSlash: true,
  // Add headers for CORS and security
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
      {
        source: '/company_knowledge.json',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Cache-Control', value: 'public, max-age=3600' },
        ],
      },
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://assets.calendly.com https://calendly.com https://js.intercomcdn.com https://widget.intercom.io",
              "style-src 'self' 'unsafe-inline' https://assets.calendly.com https://calendly.com https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com https://assets.calendly.com",
              "img-src 'self' data: blob: https: http:",
              "connect-src 'self' https://api.calendly.com https://calendly.com https://assets.calendly.com wss://nexus-websocket-a.intercom.io https://api-iam.intercom.io",
              "frame-src 'self' https://calendly.com https://assets.calendly.com",
              "media-src 'self' https://assets.calendly.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'self'"
            ].join('; ')
          }
        ],
      },
    ];
  },
};

module.exports = nextConfig;
