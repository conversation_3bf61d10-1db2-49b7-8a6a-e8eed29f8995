import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import Navbar from '@/components/navbar';
import Footer from '@/components/footer'; // Import Footer
import ChatbotEmbed from '@/components/ChatbotEmbed'; // Import ChatbotEmbed
import ScriptBlocker from '@/components/ScriptBlocker'; // Import ScriptBlocker

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'UpZera',
  description: 'AI-powered solutions for your business',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ScriptBlocker /> {/* Block unwanted tracking scripts */}
        <Navbar />
        <main>{children}</main> {/* Wrap children in main for semantic structure */}
        <Footer /> {/* Add Footer */}
        <ChatbotEmbed /> {/* Add Chatbot Embed */}
      </body>
    </html>
  );
}
